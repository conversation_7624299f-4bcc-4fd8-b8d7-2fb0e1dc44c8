// shared/query/mutations/useLoginMutation.ts
import { BetshopSettings } from '@/shared/hooks/business/useBetshopSettings';
import { useAuthStore } from '@/shared/stores/authStore'; // Import the auth store
import { useSessionTimeoutStore } from '@/shared/stores/sessionStore';
import { getAdminBackendUrl } from '@/shared/utils/envValidation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
// import { User } from '@/shared/types/global';

interface LoginCredentials {
    email: string;
    password: string;
    platformType?: string;
    rememberMe?: boolean;
}

// Extended interface for login response that includes additional admin fields
// interface LoginUser extends User {
// lastName: string; // Required in login response
// phone: string; // Required in login response
// phoneVerified: boolean;
// resetPasswordToken: string | null;
// resetPasswordSentAt: string | null;
// rememberCreatedAt: string | null;
// confirmationToken: string | null;
// confirmedAt: string | null;
// confirmationSentAt: string | null;
// unconfirmedEmail: string | null;
// deactivatedById: number | null;
// deactivatedByType: string | null;
// deactivatedAt: string | null;
// allowedCurrencies: string;
// kycRegulated: boolean;
// loginCount: number;
// secretKey: string | null;
// ipWhitelist: string;
// isAppliedIpWhitelist: boolean;
// ipWhitelistType: string;
// timezone: string;
// agentType: number;
// agentAction: string;
// loginPin: string;
// affiliateStatus: boolean;
// reportingEmail: string | null;
// reportingEmailVerified: boolean;
// }

// interface LoginRecord {
//     loginPinExist: boolean;
//     user: User;
// }

interface LoginResponse {
    success: number;
    message: string;
    record: {
        loginPinExist: boolean; // Key for 2FA
        user: {
            id: number;
            firstName: string;
            email: string;
            affiliateToken?: string; // Token might not be present if PIN is required
            timezone?: string;
            tenantId?: number; // Add tenantId to user object
            // ... other user fields from your response
        };
        tenant: {
            id: number,
            domain: string,
            name: string,
        };
        token?: string; // Sometimes token is directly here
        tokenType?: string;
        permissions?: any; // Add permissions data type if you need to store it
    };
    count: number;
}
const loginUser = async (credentials: LoginCredentials): Promise<LoginResponse> => {
    // Get validated admin backend URL
    const adminBackendUrl = getAdminBackendUrl();

    const response = await fetch(`${adminBackendUrl}/api/login/admin`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(credentials),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
    }

    const responseData = await response.json();
    return responseData;
};

export const useLoginMutation = () => {
    const queryClient = useQueryClient();
    // Get the new setTwoStepRequired and the full setAuth action
    const { setFullAuth, setTwoStepRequired } = useAuthStore();

    return useMutation<LoginResponse, Error, LoginCredentials>({
        mutationFn: loginUser,
        onSuccess: (data) => {

            if (data.success === 1 && data.record?.user) {
                // Check if 2FA is required based on message or loginPinExist field
                const requires2FA = data.record.loginPinExist || data.message?.includes('login pin');

                if (requires2FA) {
                    // User needs to provide a PIN
                    setTwoStepRequired(data.record.user.id, data.record.user.email, data.message);
                } else {
                    // No PIN required, directly authenticate
                    // **IMPORTANT**: Get the actual token from response for full auth
                    const token = data.record.token || data.record.user.affiliateToken; // Prioritize `token` field if present, else use `affiliateToken`
                    const user = data.record.user;

                    // Extract tenant information from the response if available
                    if (data.record.tenant && data.record.tenant.id) {
                        (user as any).tenantId = data.record.tenant.id;
                    }

                    if (token) {
                        setFullAuth(token, user); // Use setFullAuth for complete authentication
                        // Fetch betshop settings and store in session timeout store
                        const betshopSettingsQuery = BetshopSettings(user.tenantId);
                        if (user.tenantId !== undefined) {
                            useSessionTimeoutStore.getState().setTenantId(user.tenantId);
                        }
                        if (betshopSettingsQuery.settings) {
                            useSessionTimeoutStore.getState().setBetshopSettings(betshopSettingsQuery.settings);
                        }

                    }
                }
                queryClient.invalidateQueries({ queryKey: ['user'] }); // Invalidate user-related queries
            } else {
                throw new Error(data.message || 'Login failed with unexpected response');
            }
        },
        onError: (_error) => {
            // console.error('Login error:', error.message);
            // console.error('Full error object:', error);
            // You might want to clear any partial 2FA state here if a new login attempt fails
            useAuthStore.getState().clearTwoStep();
        },
    });
};
