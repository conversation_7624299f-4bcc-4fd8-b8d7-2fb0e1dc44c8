// shared/query/mutations/useValidatePinMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { User } from '@/shared/types/global';

interface PinValidationCredentials {
    pin: string;
    id: number;
    email: string;
}

interface PinValidationResponse {
    success: number;
    message: string;
    record: {
        validPin: boolean;
        tokenType: string;
        token: string; // The actual JWT/auth token
        user: User;
        tenant?: { id: number;[key: string]: any }; // Include tenant details if relevant to store
        permissions?: any; // Include permissions if relevant to store
    };
    count: number;
}

const validatePin = async (credentials: PinValidationCredentials): Promise<PinValidationResponse> => {
    // console.log('credentials: ', credentials);
    const response = await fetch(`${process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL}/api/login/admin/validatePin`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(credentials),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'PIN validation failed');
    }

    return response.json();
};

export const useValidatePinMutation = () => {
    const queryClient = useQueryClient();
    const setFullAuth = useAuthStore((state) => state.setFullAuth); // Action for full authentication

    return useMutation<PinValidationResponse, Error, PinValidationCredentials>({
        mutationFn: validatePin,
        onSuccess: (data) => {
            console.log(data, 'data');
            if (data.success === 1 && data.record.validPin) {
                const user = data.record.user;

                // Extract tenant information from the response if available
                if (data.record.tenant && data.record.tenant.id) {
                    (user as any).tenantId = data.record.tenant.id;
                }

                setFullAuth(data.record.token, user); // Final authentication step
                queryClient.invalidateQueries({ queryKey: ['user'] }); // Invalidate user-related queries
                // Clear any 2FA specific state, though setFullAuth should handle it
            } else {
                throw new Error(data.message || 'PIN validation failed unexpectedly');
            }
        },
        onError: (_error) => {
            // console.error('PIN validation error:', error.message);
            // Do NOT clear full auth here, only clear if they explicitly cancel 2FA or re-login.
            // If the PIN is wrong, just show error on the 2FA page.
        },
    });
};
