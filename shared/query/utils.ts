// shared/query/utils.ts
// This file is kept for backward compatibility but the QueryClient creation
// has been moved to QueryProvider.tsx for better Next.js 15 compatibility

/**
 * Generate the required timePeriod parameter with exact encoding format
 * Format: %7B%22startDate%22:%222025-06-25%2000:00:00%22,%22endDate%22:%222025-06-25%2023:59:59%22%7D
 */
export const generateTimePeriod = (inputDate?: any): string => {
    const now = new Date();
    const sevenDaysAgo = new Date(now);
    sevenDaysAgo.setDate(now.getDate() - 6); // last 7 days = today + previous 6 days

    const formatDate = (d: Date, isEnd = false) =>
        `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${isEnd ? '23:59:59' : '00:00:00'}`;

    const defaultStartDate = inputDate?.startDate || formatDate(sevenDaysAgo);
    const defaultEndDate = inputDate?.endDate || formatDate(now, true);

    const timePeriodObj = {
        startDate: defaultStartDate,
        endDate: defaultEndDate
    };

    return encodeURIComponent(JSON.stringify(timePeriodObj));
};

export const setBetShopSessionTimeout = (BetshopSettings: any, user: any, useSessionTimeoutStore: any) => {
    const betShopSettingsQuery = BetshopSettings(user.tenantId);
    if (user.tenantId !== undefined) {
        useSessionTimeoutStore.getState().setTenantId(user.tenantId);
    }
    if (betShopSettingsQuery.settings) {
        useSessionTimeoutStore.getState().setBetshopSettings(betShopSettingsQuery.settings);
    }
};
