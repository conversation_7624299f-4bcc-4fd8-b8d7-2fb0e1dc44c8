// shared/hooks/business/useBetshopSettings.ts
import { useAuthStore } from '@/shared/stores/authStore';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

interface BetshopSettingsRequest {
  tenantID: number;
}

interface BetshopSettingsItem {
  id: string;
  key: string;
  value: string;
  description?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

interface BetshopSettingsResponse {
  success: number;
  message: string;
  data: {
    settingsDetails: BetshopSettingsItem[];
  };
  errors?: string[];
}

interface BetshopSettingsError {
  success: number;
  message: string;
  errors?: string[];
}

/**
 * Fetch betshop settings from the reporting API
 */
export const fetchBetshopSettings = async (tenantId: number): Promise<BetshopSettingsResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  if (!tenantId) {
    throw new Error('Tenant ID is required');
  }

  // Use the reporting API URL
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || 'https://reporting.ingrandstation.com';

  const requestBody: BetshopSettingsRequest = {
    tenantID: tenantId
  };

  const response = await fetch(`${baseUrl}/api/v2/admin/betshop-settings`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData: BetshopSettingsError = await response.json();
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const data: BetshopSettingsResponse = await response.json();
  if (data?.data?.settingsDetails?.length <= 0) {
    throw new Error(data.message || 'Failed to fetch betshop settings');
  }

  return data;
};

/**
 * Hook to fetch betshop settings
 */
export const BetshopSettings = (tenantId?: number) => {
  const { user, isAuthenticated } = useAuthStore();

  // Use tenantId from parameter or fallback to user's tenantId
  const effectiveTenantId = tenantId || user?.tenantId;

  const query = useQuery<BetshopSettingsResponse>({
    queryKey: ['betshopSettings', effectiveTenantId],
    queryFn: () => fetchBetshopSettings(effectiveTenantId!),
    enabled: isAuthenticated && !!effectiveTenantId,
    staleTime: 2 * 60 * 1000, // 10 minutes
    retry: 2,
  });

  // Memoize the return value to prevent unnecessary re-renders
  // Only memoize the specific values we need, not the entire query object
  return useMemo(() => ({
    data: query.data,
    isLoading: query.isLoading,
    error: query.error,
    status: query.status,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    refetch: query.refetch,
    settings: query.data?.data?.settingsDetails || [],
  }), [
    query.data,
    query.isLoading,
    query.error,
    query.status,
    query.isError,
    query.isSuccess,
    query.isFetching,
    query.refetch
  ]);
};

