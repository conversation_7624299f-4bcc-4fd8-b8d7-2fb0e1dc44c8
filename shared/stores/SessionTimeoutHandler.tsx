import React, { useState } from 'react';
import { useBetshopSettingsTimeout } from './useBetshopSettingsTimeout';
import { useAuthStore } from './authStore';
import { useRouter } from 'next/router';

// Simple modal component
function SessionExpiredModal({ open, onClose }: { open: boolean; onClose: () => void }) {
    if (!open) return null;
    return (
        <div
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100vw',
                height: '100vh',
                background: 'rgba(0,0,0,0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 9999,
            }}
            onClick={onClose}
        >
            <div
                style={{
                    background: '#fff',
                    padding: '2rem',
                    borderRadius: '8px',
                    minWidth: '300px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                }}
                onClick={e => e.stopPropagation()}
            >
                <h2 style={{ marginBottom: '1rem' }}>Session Expired</h2>
                <p style={{ marginBottom: '2rem' }}>Your session has expired, please login again.</p>
                <button
                    style={{ padding: '0.5rem 1rem', borderRadius: '4px', background: '#0070f3', color: '#fff', border: 'none' }}
                    onClick={onClose}
                >
                    OK
                </button>
            </div>
        </div>
    );
}

// Example usage in a component
export function SessionTimeoutHandler() {
    const [modalOpen, setModalOpen] = useState(false);

    const handleTimeout = () => {
        setModalOpen(true);
    };

    const router = useRouter();
    const handleClose = () => {
        setModalOpen(false);
        useAuthStore.getState().clearAuth();
        router.replace('/authentication/sign-in');
    };

    // Example: get timeout from betshopSettings, fallback to 5000ms
    useBetshopSettingsTimeout(handleTimeout, (settings) => settings?.timeoutMs || 5000);

    return <SessionExpiredModal open={modalOpen} onClose={handleClose} />;
}
