import { useEffect, useRef } from 'react';
import { useSessionTimeoutStore } from './sessionStore';

/**
 * Hook to trigger an action (e.g., popup) after a timeout when betshopSettings changes.
 * @param onTimeout Callback to execute when timeout completes
 * @param getTimeoutMs Function to extract timeout duration (ms) from betshopSettings
 */
export function useBetshopSettingsTimeout(
    onTimeout: () => void,
    getTimeoutMs: (settings: any) => number
) {
    const betshopSettings = useSessionTimeoutStore((state) => state.betshopSettings);
    console.log(betshopSettings, 'ssss');
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    useEffect(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        if (betshopSettings) {
            const ms = getTimeoutMs(betshopSettings);
            if (ms && ms > 0) {
                timeoutRef.current = setTimeout(() => {
                    onTimeout();
                }, ms);
            }
        }
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [betshopSettings, onTimeout, getTimeoutMs]);
}

